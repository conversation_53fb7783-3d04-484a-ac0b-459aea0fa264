import type {
  ImageGenerationRequest,
  ImageGenerationResponse,
} from '@/lib/ai/types';
import { uploadFile } from '@/storage';
import { type NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  const requestId = Math.random().toString(36).substring(7);

  try {
    let prompt: string;
    let model: string | undefined;
    let imageFile: File | undefined;

    // Check if request is FormData (for image upload) or JSON
    const contentType = request.headers.get('content-type') || '';

    if (contentType.includes('multipart/form-data')) {
      // Handle FormData (image upload)
      const formData = await request.formData();
      prompt = formData.get('prompt') as string;
      model = formData.get('model') as string | undefined;
      imageFile = (formData.get('image') as File | null) || undefined;
    } else {
      // Handle JSON (text-to-image)
      const body: ImageGenerationRequest = await request.json();
      prompt = body.prompt;
      model = body.model;
    }

    if (!prompt?.trim()) {
      return NextResponse.json(
        {
          success: false,
          error: 'Prompt cannot be empty',
        } as ImageGenerationResponse,
        { status: 400 }
      );
    }

    // Generate image using your custom API
    const result = await generateWithCustomAPI(
      prompt,
      model,
      imageFile,
      requestId
    );

    return NextResponse.json(result);
  } catch (error) {
    console.error(`Error generating image [requestId=${requestId}]: `, error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Generation failed',
      } as ImageGenerationResponse,
      { status: 500 }
    );
  }
}

// Custom API handler for your third-party service
async function generateWithCustomAPI(
  prompt: string,
  model?: string,
  imageFile?: File,
  requestId?: string
): Promise<ImageGenerationResponse> {
  const apiKey = process.env.AI_IMAGE_API_KEY;
  const baseUrl = process.env.AI_IMAGE_BASE_URL;

  if (!apiKey || !baseUrl) {
    throw new Error('AI API configuration missing');
  }

  let requestBody: any;

  if (imageFile) {
    // For image-to-image generation, first upload the image to get a URL
    const imageBuffer = Buffer.from(await imageFile.arrayBuffer());
    const mimeType = imageFile.type || 'image/png';
    const filename = `input-image-${Date.now()}.${mimeType.split('/')[1]}`;

    const uploadResult = await uploadFile(
      imageBuffer,
      filename,
      mimeType,
      'ai-input'
    );

    console.log(`[${requestId}] Uploaded input image:`, uploadResult.url);

    requestBody = {
      model: model || process.env.AI_IMAGE_MODEL || 'nano-banana-fast',
      prompt,
      urls: [uploadResult.url],
      webHook: '-1',
      shutProgress: false,
    };
  } else {
    // Text-to-image generation
    requestBody = {
      model: model || process.env.AI_IMAGE_MODEL || 'nano-banana-fast',
      prompt,
      aspectRatio: '1:1',
      webHook: '-1',
      shutProgress: false,
    };
  }

  const apiUrl = `${baseUrl}/v1/draw/nano-banana`;

  const response = await fetch(apiUrl, {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${apiKey}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(requestBody),
  });

  if (!response.ok) {
    const errorText = await response.text();
    console.error(`[${requestId}] API error response:`, errorText);

    let errorData: any;
    try {
      errorData = JSON.parse(errorText);
    } catch {
      errorData = { error: { message: errorText } };
    }

    throw new Error(
      errorData?.error?.message ||
        errorData?.message ||
        `API error: ${response.status}`
    );
  }

  const initialData = await response.json();

  if (initialData.code !== 200 && initialData.code !== 0) {
    throw new Error(
      initialData.msg || 'API returned error code: ' + initialData.code
    );
  }

  const taskId = initialData.data?.id;
  if (!taskId) {
    throw new Error('No task ID returned from API');
  }

  console.log(`[${requestId}] Task ID:`, taskId);

  // Poll for results
  const resultUrl = `${baseUrl}/v1/draw/result`;
  let pollAttempts = 0;
  const maxPolls = 30; // Maximum 5 minutes (30 * 10 seconds)

  while (pollAttempts < maxPolls) {
    console.log(
      `[${requestId}] Polling attempt ${pollAttempts + 1}/${maxPolls}`
    );

    const resultResponse = await fetch(resultUrl, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ id: taskId }),
    });

    if (!resultResponse.ok) {
      console.error(
        `[${requestId}] Result polling failed:`,
        resultResponse.status
      );
      await new Promise((resolve) => setTimeout(resolve, 10000));
      pollAttempts++;
      continue;
    }

    const resultData = await resultResponse.json();
    console.log(
      `[${requestId}] Poll result:`,
      JSON.stringify(resultData, null, 2)
    );

    const imageUrl = resultData.data?.url || resultData.data?.results?.[0]?.url;
    const status = resultData.data?.status;

    console.log(`[${requestId}] Status: ${status}, ImageUrl: ${imageUrl}`);

    if (status === 'succeeded' && imageUrl) {
      console.log(`[${requestId}] Image generation completed:`, imageUrl);

      // Download and persist the image to R2 storage
      try {
        console.log(`[${requestId}] Starting image persistence to R2...`);

        // Add timeout to prevent hanging
        const persistPromise = downloadAndPersistImage(imageUrl);
        const timeoutPromise = new Promise<string>((_, reject) =>
          setTimeout(() => reject(new Error('Persist timeout')), 30000)
        );

        const permanentUrl = await Promise.race([
          persistPromise,
          timeoutPromise,
        ]);
        console.log(`[${requestId}] Image persisted to R2:`, permanentUrl);

        return {
          success: true,
          imageUrl: permanentUrl,
        };
      } catch (persistError) {
        console.error(`[${requestId}] Failed to persist image:`, persistError);
        // Fallback to temporary URL if persist fails
        console.log(
          `[${requestId}] Using temporary URL as fallback:`,
          imageUrl
        );
        return {
          success: true,
          imageUrl,
        };
      }
    } else if (status === 'failed') {
      throw new Error(
        resultData.data?.failure_reason ||
          resultData.data?.error ||
          'Image generation failed'
      );
    } else {
      console.log(
        `[${requestId}] Still processing... Status: ${status}, continuing to poll`
      );
    }

    // Wait before next poll
    await new Promise((resolve) => setTimeout(resolve, 10000));
    pollAttempts++;
  }

  throw new Error('Image generation timed out after polling');
}

// Download image from temporary URL and persist to R2 storage
async function downloadAndPersistImage(imageUrl: string): Promise<string> {
  console.log('Starting download from temporary URL:', imageUrl);

  const imageResponse = await fetch(imageUrl);
  if (!imageResponse.ok) {
    throw new Error(`Failed to download image: ${imageResponse.status}`);
  }

  console.log('Image downloaded successfully, processing buffer...');
  const imageBuffer = Buffer.from(await imageResponse.arrayBuffer());
  const urlPath = new URL(imageUrl).pathname;
  const extension = urlPath.split('.').pop() || 'png';
  const filename = `generated-image-${Date.now()}.${extension}`;

  console.log('Uploading to R2 with filename:', filename);
  const contentType = getContentTypeFromExtension(extension);
  const result = await uploadFile(
    imageBuffer,
    filename,
    contentType,
    'ai-generated'
  );

  console.log('Upload to R2 completed:', result.url);
  return result.url;
}

function getContentTypeFromExtension(extension: string): string {
  const contentTypes: { [key: string]: string } = {
    png: 'image/png',
    jpg: 'image/jpeg',
    jpeg: 'image/jpeg',
    webp: 'image/webp',
    gif: 'image/gif',
  };

  return contentTypes[extension.toLowerCase()] || 'image/png';
}
