import { constructMetadata } from '@/lib/metadata';
import { getUrlWithLocale } from '@/lib/urls/urls';
import { Image, Sparkles, Wand2 } from 'lucide-react';
import type { Metadata } from 'next';
import type { Locale } from 'next-intl';
import { getTranslations } from 'next-intl/server';
import Link from 'next/link';

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: Locale }>;
}): Promise<Metadata | undefined> {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'Metadata' });

  return constructMetadata({
    title: 'AI Apps | ' + t('title'),
    description:
      'Collection of AI-powered applications for image generation, editing, and creative tools',
    canonicalUrl: getUrlWithLocale('/apps', locale),
  });
}

const apps = [
  {
    id: 'figurine',
    title: 'AI Figurine Generator',
    description: 'Transform any character into stunning figurine models',
    icon: '🎭',
    gradient: 'from-orange-400 to-amber-400',
    bgGradient: 'from-orange-50 via-amber-50 to-yellow-50',
    href: '/apps/figurine',
    cost: 2,
    features: [
      'Upload character image',
      'Custom figurine styles',
      'High-quality results',
    ],
  },
  // Future apps can be added here
];

export default async function AppsPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      <div className="container mx-auto px-4 py-16">
        {/* Header Section */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-gradient-to-r from-blue-100 to-purple-100 text-blue-700 text-sm font-medium mb-6">
            <Sparkles className="size-4" />
            AI Applications
          </div>

          <h1 className="text-5xl font-bold text-gray-900 mb-6">
            AI-Powered Creative Tools
          </h1>

          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Discover our collection of AI applications designed to enhance your
            creativity and productivity
          </p>
        </div>

        {/* Apps Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {apps.map((app) => (
            <Link key={app.id} href={app.href}>
              <div
                className={`group relative overflow-hidden rounded-2xl bg-gradient-to-br ${app.bgGradient} p-8 shadow-lg border border-white/50 backdrop-blur-sm hover:shadow-xl transition-all duration-300 hover:-translate-y-2 cursor-pointer`}
              >
                {/* App Icon */}
                <div className="text-6xl mb-6 group-hover:scale-110 transition-transform duration-300">
                  {app.icon}
                </div>

                {/* App Info */}
                <div className="space-y-4">
                  <div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-2">
                      {app.title}
                    </h3>
                    <p className="text-gray-700">{app.description}</p>
                  </div>

                  {/* Features */}
                  <ul className="space-y-2">
                    {app.features.map((feature, index) => (
                      <li
                        key={index}
                        className="flex items-center gap-2 text-sm text-gray-600"
                      >
                        <div className="size-1.5 rounded-full bg-gray-400" />
                        {feature}
                      </li>
                    ))}
                  </ul>

                  {/* Cost */}
                  <div className="flex items-center gap-2 text-orange-600 font-semibold">
                    <Sparkles className="size-4 fill-current" />
                    <span>{app.cost} credits per generation</span>
                  </div>
                </div>

                {/* Hover Effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-white/0 to-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              </div>
            </Link>
          ))}

          {/* Coming Soon Cards */}
          <div className="group relative overflow-hidden rounded-2xl bg-gradient-to-br from-gray-100 to-gray-200 p-8 shadow-lg border border-gray-200 backdrop-blur-sm">
            <div className="text-6xl mb-6 opacity-50">
              <Image className="size-16 text-gray-400" />
            </div>

            <div className="space-y-4">
              <div>
                <h3 className="text-2xl font-bold text-gray-500 mb-2">
                  More Apps Coming Soon
                </h3>
                <p className="text-gray-500">
                  Stay tuned for more AI-powered creative tools
                </p>
              </div>

              <div className="inline-flex items-center gap-2 px-3 py-1 rounded-full bg-gray-300 text-gray-600 text-sm font-medium">
                <Wand2 className="size-3" />
                Coming Soon
              </div>
            </div>
          </div>
        </div>

        {/* Stats Section */}
        <div className="mt-20 grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
          <div className="text-center">
            <div className="text-4xl font-bold text-gray-900 mb-2">1+</div>
            <div className="text-gray-600">AI Applications</div>
          </div>
          <div className="text-center">
            <div className="text-4xl font-bold text-gray-900 mb-2">∞</div>
            <div className="text-gray-600">Creative Possibilities</div>
          </div>
          <div className="text-center">
            <div className="text-4xl font-bold text-gray-900 mb-2">⚡</div>
            <div className="text-gray-600">Lightning Fast</div>
          </div>
        </div>
      </div>
    </div>
  );
}
