'use client';

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ImageResult,
  ImageUploadGrid,
  PromptInput,
  StyleSelector,
} from '@/components/ai';
import type { Style } from '@/components/ai/style-selector';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { useImageGeneration } from '@/hooks/use-image-generation';
import { useToast } from '@/hooks/use-toast';
import { FIGURINE_PROMPTS, FIGURINE_STYLES } from '@/lib/ai/figurine';
import type { ImageGenerationRequest } from '@/lib/ai/types';
import { ArrowLeft, Info, Palette, Sparkles, Upload } from 'lucide-react';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { useState } from 'react';

export default function FigurinePage() {
  const [uploadedImages, setUploadedImages] = useState<File[]>([]);
  const [prompt, setPrompt] = useState(FIGURINE_PROMPTS.default);
  const [selectedStyle, setSelectedStyle] = useState<Style | null>(null);
  const [infoPopoverOpen, setInfoPopoverOpen] = useState(false);
  const { toast } = useToast();
  const { loading, error, result, generateImage } = useImageGeneration();
  const tNavbar = useTranslations('Marketing.navbar');
  const tCommon = useTranslations('AIApps.common');

  const handleGenerate = async () => {
    if (!prompt.trim()) {
      toast({
        title: tCommon('errors.emptyPrompt.title'),
        description: tCommon('errors.emptyPrompt.description'),
        variant: 'destructive',
      });
      return;
    }

    if (uploadedImages.length === 0) {
      toast({
        title: tCommon('errors.noImage.title'),
        description: tCommon('errors.noImage.description'),
        variant: 'destructive',
      });
      return;
    }

    try {
      // Use the first image for now, but could be extended for batch processing
      const request: ImageGenerationRequest = {
        prompt,
        image: uploadedImages[0],
      };

      await generateImage(request);
    } catch (error) {
      console.error('Generation failed:', error);
    }
  };

  const handleStyleSelect = (style: Style) => {
    setSelectedStyle(style);
    // Auto-fill prompt with style-specific text
    setPrompt(style.prompt);
  };

  return (
    <div>
      <div className="container mx-auto px-4 py-8">
        {/* Back Button */}
        <div className="mb-6">
          <Link
            href="/apps"
            className="inline-flex items-center gap-2 text-muted-foreground hover:text-foreground transition-colors"
          >
            <ArrowLeft className="size-4" />
            {tCommon('backToApps')}
          </Link>
        </div>

        {/* Header Section */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-orange-100 dark:bg-orange-900/20 text-orange-700 dark:text-orange-200 text-sm font-medium mb-6">
            <Sparkles className="size-4" />
            {tNavbar('apps.items.figurine.title')}
          </div>

          <h1 className="text-4xl font-bold text-foreground mb-4">
            {tNavbar('apps.items.figurine.description')}
          </h1>
        </div>

        {/* Main Generator */}
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-stretch">
            {/* Left Column - Upload and Controls */}
            <Card className="flex flex-col">
              <CardContent className="space-y-4 flex-1">
                {/* Upload Images Section */}
                <div>
                  <div className="mb-2">
                    <h3 className="text-xl font-semibold text-foreground flex items-center gap-2">
                      <Upload className="size-5 text-orange-600" />
                      {tCommon('uploadImage.title')}
                      <Popover open={infoPopoverOpen} onOpenChange={setInfoPopoverOpen}>
                        <PopoverTrigger asChild>
                          <Info className="size-4 text-muted-foreground hover:text-foreground cursor-pointer transition-colors" />
                        </PopoverTrigger>
                        <PopoverContent 
                          avoidCollisions={true}
                          collisionPadding={10}
                          className="max-w-xs bg-background border border-border text-foreground shadow-md"
                        >
                          <div className="space-y-1">
                            <div className="text-sm font-medium text-foreground">
                              {uploadedImages.length} / 9 {tCommon('uploadImage.maxImages')}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {tCommon('uploadImage.supportedFormats')}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {tCommon('uploadImage.maxFileSize')}
                            </div>
                          </div>
                        </PopoverContent>
                      </Popover>
                    </h3>
                  </div>
                  <ImageUploadGrid
                    onImagesChange={setUploadedImages}
                    disabled={loading}
                    maxImages={9}
                  />
                </div>

                {/* Style Selection Section */}
                <div>
                  <div className="mb-2">
                    <h3 className="text-xl font-semibold text-foreground flex items-center gap-2">
                      <Palette className="size-5 text-orange-600" />
                      {tCommon('styleSelector.title')}
                    </h3>
                  </div>
                  <StyleSelector
                    styles={FIGURINE_STYLES}
                    onStyleSelect={handleStyleSelect}
                    selectedStyleId={selectedStyle?.id}
                    disabled={loading}
                  />
                </div>

                {/* Edit Instructions Section */}
                <div>
                  <div className="mb-2">
                    <h3 className="text-xl font-semibold text-foreground flex items-center gap-2">
                      <Sparkles className="size-5 text-orange-600" />
                      {tCommon('prompt.title')}
                    </h3>
                  </div>
                  <div className="space-y-4">
                    <PromptInput
                      value={prompt}
                      onChange={setPrompt}
                      disabled={loading}
                      placeholder={tCommon('prompt.placeholder')}
                      label=""
                      className="min-h-32"
                      maxLength={500}
                    />

                    <GenerateButton
                      onClick={handleGenerate}
                      loading={loading}
                      disabled={!prompt.trim() || uploadedImages.length === 0}
                      credits={2}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Right Column - Results */}
            <Card>
              <CardHeader className="pb-4">
                <CardTitle className="text-xl font-semibold text-foreground">
                  {tCommon('result.title')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <ImageResult
                  result={result}
                  loading={loading}
                  error={error}
                />
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
