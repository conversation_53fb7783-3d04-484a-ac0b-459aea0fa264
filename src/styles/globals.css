@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

/*
 * 1. Themes Documentation
 * https://mksaas.com/docs/themes

 * 2. Theme Generator
 * https://ui.shadcn.com/themes
 * https://tweakcn.com/
 * https://ui.pub/x/theme-gen
 *
 * default theme: custom theme inspired by Qoder
 * https://qoder.com/
 * https://tweakcn.com/editor/theme
 *
 * NOTICE: when you change the theme, you need to check the fonts and keep the animation variables
 *
 * 3. How to Add a Theme Selector to Your Next.js App
 * https://ouassim.tech/notes/how-to-add-a-theme-selector-to-your-nextjs-app/
 */
@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);

  /* fonts */
  --font-sans: var(--font-noto-sans);
  --font-mono: var(--font-noto-sans-mono);
  --font-serif: var(--font-noto-serif);
  --font-bricolage-grotesque: var(--font-bricolage-grotesque);

  /* animate */
  --animate-shiny-text: shiny-text 8s infinite;
  --animate-rainbow: rainbow var(--speed, 2s) infinite linear;
  --animate-accordion-down: accordion-down 0.2s ease-out;
  --animate-accordion-up: accordion-up 0.2s ease-out;
  --animate-marquee: marquee var(--duration) infinite linear;
  --animate-marquee-vertical: marquee-vertical var(--duration) linear infinite;
  --animate-ripple: ripple var(--duration, 2s) ease calc(var(--i, 0) * .2s)
    infinite;
  --animate-pulse: pulse var(--duration) ease-out infinite;
  --animate-meteor: meteor 5s linear infinite;
  --animate-gradient: gradient 8s linear infinite;

  @keyframes shiny-text {
    0%,
    90%,
    100% {
      background-position: calc(-100% - var(--shiny-width)) 0;
    }
    30%,
    60% {
      background-position: calc(100% + var(--shiny-width)) 0;
    }
  }

  @keyframes rainbow {
    0% {
      background-position: 0%;
    }
    100% {
      background-position: 200%;
    }
  }

  @keyframes accordion-down {
    from {
      height: 0;
    }
    to {
      height: var(--radix-accordion-content-height);
    }
  }

  @keyframes accordion-up {
    from {
      height: var(--radix-accordion-content-height);
    }
    to {
      height: 0;
    }
  }

  @keyframes marquee {
    from {
      transform: translateX(0);
    }
    to {
      transform: translateX(calc(-100% - var(--gap)));
    }
  }

  @keyframes marquee-vertical {
    from {
      transform: translateY(0);
    }
    to {
      transform: translateY(calc(-100% - var(--gap)));
    }
  }

  @keyframes ripple {
    0%,
    100% {
      transform: translate(-50%, -50%) scale(1);
    }
    50% {
      transform: translate(-50%, -50%) scale(0.9);
    }
  }

  @keyframes pulse {
    0%,
    100% {
      box-shadow: 0 0 0 0 var(--pulse-color);
    }
    50% {
      box-shadow: 0 0 0 8px var(--pulse-color);
    }
  }

  @keyframes meteor {
    0% {
      transform: rotate(var(--angle)) translateX(0);
      opacity: 1;
    }
    70% {
      opacity: 1;
    }
    100% {
      transform: rotate(var(--angle)) translateX(-500px);
      opacity: 0;
    }
  }

  @keyframes gradient {
    to {
      background-position: var(--bg-size, 300%) 0;
    }
  }
}

:root {
  --background: oklch(1.0 0 0);
  --foreground: oklch(0.1448 0 0);
  --card: oklch(1.0 0 0);
  --card-foreground: oklch(0.1448 0 0);
  --popover: oklch(1.0 0 0);
  --popover-foreground: oklch(0.1448 0 0);
  --primary: oklch(0.6271 0.1699 149.2138);
  --primary-foreground: oklch(0.9851 0 0);
  --secondary: oklch(0.9702 0 0);
  --secondary-foreground: oklch(0.2046 0 0);
  --muted: oklch(0.9702 0 0);
  --muted-foreground: oklch(0.5555 0 0);
  --accent: oklch(0.9819 0.0181 155.8263);
  --accent-foreground: oklch(0.4479 0.1083 151.3277);
  --destructive: oklch(0.5771 0.2152 27.325);
  --destructive-foreground: oklch(0.9851 0 0);
  --border: oklch(0.9219 0 0);
  --input: oklch(0.9219 0 0);
  --ring: oklch(0.7227 0.192 149.5793);
  --chart-1: oklch(0.7227 0.192 149.5793);
  --chart-2: oklch(0.5555 0 0);
  --chart-3: oklch(0.1448 0 0);
  --chart-4: oklch(0.7155 0 0);
  --chart-5: oklch(0.9219 0 0);
  --sidebar: oklch(0.9702 0 0);
  --sidebar-foreground: oklch(0.5555 0 0);
  --sidebar-primary: oklch(0.6271 0.1699 149.2138);
  --sidebar-primary-foreground: oklch(0.9851 0 0);
  --sidebar-accent: oklch(0.9819 0.0181 155.8263);
  --sidebar-accent-foreground: oklch(0.4479 0.1083 151.3277);
  --sidebar-border: oklch(0.9219 0 0);
  --sidebar-ring: oklch(0.7227 0.192 149.5793);
  --font-sans: var(--font-noto-sans);
  --font-serif: var(--font-noto-serif);
  --font-mono: var(--font-noto-sans-mono);
  --radius: 0.5rem;
  --shadow-2xs: 0px 4px 8px 0px hsl(0 0% 0% / 0.03);
  --shadow-xs: 0px 4px 8px 0px hsl(0 0% 0% / 0.03);
  --shadow-sm: 0px 4px 8px 0px hsl(0 0% 0% / 0.05), 0px 1px 2px -1px
    hsl(0 0% 0% / 0.05);
  --shadow: 0px 4px 8px 0px hsl(0 0% 0% / 0.05), 0px 1px 2px -1px
    hsl(0 0% 0% / 0.05);
  --shadow-md: 0px 4px 8px 0px hsl(0 0% 0% / 0.05), 0px 2px 4px -1px
    hsl(0 0% 0% / 0.05);
  --shadow-lg: 0px 4px 8px 0px hsl(0 0% 0% / 0.05), 0px 4px 6px -1px
    hsl(0 0% 0% / 0.05);
  --shadow-xl: 0px 4px 8px 0px hsl(0 0% 0% / 0.05), 0px 8px 10px -1px
    hsl(0 0% 0% / 0.05);
  --shadow-2xl: 0px 4px 8px 0px hsl(0 0% 0% / 0.13);
}

.dark {
  --background: oklch(0.1591 0 0);
  --foreground: oklch(0.9702 0 0);
  --card: oklch(0.2046 0 0);
  --card-foreground: oklch(0.9702 0 0);
  --popover: oklch(0.2046 0 0);
  --popover-foreground: oklch(0.9702 0 0);
  --primary: oklch(0.8003 0.1821 151.711);
  --primary-foreground: oklch(0.1591 0 0);
  --secondary: oklch(0.9702 0 0);
  --secondary-foreground: oklch(0.2046 0 0);
  --muted: oklch(0.2686 0 0);
  --muted-foreground: oklch(0.7155 0 0);
  --accent: oklch(0.2638 0.0276 154.8977);
  --accent-foreground: oklch(0.8003 0.1821 151.711);
  --destructive: oklch(0.3958 0.1331 25.723);
  --destructive-foreground: oklch(0.9702 0 0);
  --border: oklch(0.4 0 0);
  --input: oklch(0.4 0 0);
  --ring: oklch(0.8003 0.1821 151.711);
  --chart-1: oklch(0.8003 0.1821 151.711);
  --chart-2: oklch(0.7155 0 0);
  --chart-3: oklch(0.9702 0 0);
  --chart-4: oklch(0.4386 0 0);
  --chart-5: oklch(0.2686 0 0);
  --sidebar: oklch(0.22 0 0);
  --sidebar-foreground: oklch(0.7155 0 0);
  --sidebar-primary: oklch(0.8003 0.1821 151.711);
  --sidebar-primary-foreground: oklch(0.1591 0 0);
  --sidebar-accent: oklch(0.2638 0.0276 154.8977);
  --sidebar-accent-foreground: oklch(0.8003 0.1821 151.711);
  --sidebar-border: oklch(0.2686 0 0);
  --sidebar-ring: oklch(0.8003 0.1821 151.711);
  --font-sans: var(--font-noto-sans);
  --font-serif: var(--font-noto-serif);
  --font-mono: var(--font-noto-sans-mono);
  --radius: 0.5rem;
  --shadow-2xs: 0px 4px 8px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0px 4px 8px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0px 4px 8px 0px hsl(0 0% 0% / 0.1), 0px 1px 2px -1px
    hsl(0 0% 0% / 0.1);
  --shadow: 0px 4px 8px 0px hsl(0 0% 0% / 0.1), 0px 1px 2px -1px
    hsl(0 0% 0% / 0.1);
  --shadow-md: 0px 4px 8px 0px hsl(0 0% 0% / 0.1), 0px 2px 4px -1px
    hsl(0 0% 0% / 0.1);
  --shadow-lg: 0px 4px 8px 0px hsl(0 0% 0% / 0.1), 0px 4px 6px -1px
    hsl(0 0% 0% / 0.1);
  --shadow-xl: 0px 4px 8px 0px hsl(0 0% 0% / 0.1), 0px 8px 10px -1px
    hsl(0 0% 0% / 0.1);
  --shadow-2xl: 0px 4px 8px 0px hsl(0 0% 0% / 0.25);
}

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentColor);
  }
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground;
  }
}

body {
  @apply overscroll-none bg-transparent;
}

.text-gradient_indigo-purple {
  background: linear-gradient(90deg, #6366f1 0%, rgb(168 85 247 / 0.8) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/*
 * 1. all the themes are copied from the shadcn-ui dashboard example
 * https://github.com/shadcn-ui/ui/blob/main/apps/v4/app/(examples)/dashboard/theme.css
 * https://github.com/TheOrcDev/orcish-dashboard/blob/main/app/globals.css
 *
 * 2. we suggest always using the default theme for better user experience,
 * and then override the .theme-default to customize the theme
 */
.theme-default {
  /* default theme */
}

.theme-neutral {
  --primary: var(--color-neutral-600);
  --primary-foreground: var(--color-neutral-50);

  @variant dark {
    --primary: var(--color-neutral-500);
    --primary-foreground: var(--color-neutral-50);
  }
}

.theme-blue {
  --primary: var(--color-blue-600);
  --primary-foreground: var(--color-blue-50);

  @variant dark {
    --primary: var(--color-blue-500);
    --primary-foreground: var(--color-blue-50);
  }
}

.theme-green {
  --primary: var(--color-lime-600);
  --primary-foreground: var(--color-lime-50);

  @variant dark {
    --primary: var(--color-lime-600);
    --primary-foreground: var(--color-lime-50);
  }
}

.theme-amber {
  --primary: var(--color-amber-600);
  --primary-foreground: var(--color-amber-50);

  @variant dark {
    --primary: var(--color-amber-500);
    --primary-foreground: var(--color-amber-50);
  }
}

/* https://github.com/shadcn-ui/ui/issues/4227#issuecomment-2438290165 */
html body[data-scroll-locked] {
  overflow: visible !important;
  margin-right: 0 !important;
}

/* Fix for Fumadocs empty banner appearing in Cloudflare Worker */
/* This targets the specific banner issue where empty:hidden doesn't work properly */
div[class*="border-t"][class*="bg-fd-secondary"]:not(:has(*)):not(
    [data-content]
  ) {
  display: none !important;
}

/* Fallback for banners with the exact classes found in the issue */
.border-t.bg-fd-secondary\/50.p-3:empty {
  display: none !important;
}

/* Additional safety for any empty Fumadocs banner */
[class*="fd-secondary"]:empty:not([data-banner-content]) {
  display: none !important;
}
