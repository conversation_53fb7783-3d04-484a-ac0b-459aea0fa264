// AI image generation type definitions

export interface ImageGenerationRequest {
  prompt: string;
  provider?: string;
  model?: string;
  width?: number;
  height?: number;
  image?: File; // For image-to-image generation
}

export interface ImageGenerationResponse {
  success: boolean;
  imageUrl?: string;
  error?: string;
}

// Base AI configuration interface
export interface AIConfig {
  baseUrl: string;
  apiKey: string;
  model: string;
}

// Image generation specific configuration
export interface AIImageConfig extends AIConfig {
  // Future: image-specific options like maxWidth, maxHeight, etc.
}

// Text processing specific configuration
export interface AITextConfig extends AIConfig {
  // Future: text-specific options like maxTokens, temperature, etc.
}

// Default image generation configuration
export const DEFAULT_AI_IMAGE_CONFIG: AIImageConfig = {
  baseUrl: process.env.AI_IMAGE_BASE_URL || '',
  apiKey: process.env.AI_IMAGE_API_KEY || '',
  model: process.env.AI_IMAGE_MODEL || '',
};

// Default text processing configuration (for future use)
export const DEFAULT_AI_TEXT_CONFIG: AITextConfig = {
  baseUrl: process.env.AI_TEXT_BASE_URL || '',
  apiKey: process.env.AI_TEXT_API_KEY || '',
  model: process.env.AI_TEXT_MODEL || '',
};
