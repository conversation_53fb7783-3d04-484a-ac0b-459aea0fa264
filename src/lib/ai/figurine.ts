import type { Style } from '@/components/ai/style-selector';

/**
 * Figurine AI Generation Configuration
 *
 * This file contains all figurine-specific configurations including styles, prompts,
 * and their relationships. Each style has a corresponding prompt optimized for that
 * specific figurine type.
 */

export interface FigurineStyle extends Style {
  // Could extend with figurine-specific properties in the future
  category?: 'collectible' | 'action' | 'statue' | 'custom';
}

export const FIGURINE_CONFIG = {
  /**
   * Style-specific prompts for different figurine types
   * Each style has its own optimized prompt for best results
   */
  styles: [
    {
      id: 'articulated',
      name: 'Articulated Figure',
      preview: '/blocks/exercice.png',
      prompt:
        "create a highly articulated action figure of the character with poseable joints, detailed sculpting, and premium finish. Show multiple connection points and realistic joint mechanisms. Include accessories and interchangeable parts. Display on a dynamic pose with professional photography lighting, emphasizing the figure's articulation and collectible quality.",
      category: 'action',
    },
    {
      id: 'statue',
      name: 'Static Statue',
      preview: '/blocks/card.png',
      prompt:
        'generate a museum-quality static statue figure in a fixed dramatic pose. Focus on intricate sculpting details, premium materials like resin or PVC, and flawless paint application. Place on an elegant display base with character nameplate. Use gallery lighting to emphasize the artistic craftsmanship and premium collectible nature.',
      category: 'statue',
    },
    {
      id: 'chibi',
      name: 'Chibi Style',
      preview: '/blocks/music.png',
      prompt:
        "create an adorable chibi-style figurine with cute deformed proportions, oversized head, and simplified features. Emphasize the kawaii aesthetic with bright colors, glossy finish, and expressive details. Include a themed base that complements the character's personality. Perfect for display collection with soft, cheerful lighting.",
      category: 'collectible',
    },
    {
      id: 'scale-figure',
      name: 'Scale Figure',
      preview: '/blocks/charts.png',
      prompt:
        "generate a premium 1/7 scale anime figure with accurate proportions, detailed character design, and professional-grade painting. Capture the original artwork's essence with vibrant colors and precise facial features. Include a themed base and display in a collector's setup with proper lighting to showcase the figure's quality.",
      category: 'collectible',
    },
    {
      id: 'garage-kit',
      name: 'Garage Kit',
      preview: '/blocks/payments.png',
      prompt:
        'create a limited edition garage kit figure with exceptional detail and craftsmanship. Show the resin cast quality with intricate sculpting, perfect for serious collectors. Include assembly components and detailed painting guide. Present as a premium, limited-run collectible with packaging that emphasizes its exclusivity and handcrafted nature.',
      category: 'custom',
    },
    {
      id: 'nendoroid',
      name: 'Nendoroid Style',
      preview: '/blocks/mail2.png',
      prompt:
        'design a Nendoroid-style figure with the characteristic deformed proportions, interchangeable face plates, and modular accessories. Show multiple expression options and pose variations. Include the signature Nendoroid base and packaging style. Emphasize the playful, customizable nature with bright, cheerful presentation.',
      category: 'action',
    },
    {
      id: 'figma',
      name: 'Figma Style',
      preview: '/blocks/origin-cal.png',
      prompt:
        "create a Figma-style action figure with realistic proportions and extensive articulation. Showcase the figure's dynamic posing capabilities with multiple joint points and accessories. Include interchangeable hands, weapons, or props. Present with action-oriented photography that demonstrates the figure's flexibility and detail quality.",
      category: 'action',
    },
    {
      id: 'prize-figure',
      name: 'Prize Figure',
      preview: '/images/blog/post-1.png',
      prompt:
        'generate a colorful prize figure with vibrant design and accessible collectible appeal. Focus on eye-catching colors, solid construction, and charming character representation. Show in an arcade or prize collection setting with bright, fun lighting that emphasizes its appeal as an affordable, cheerful collectible.',
      category: 'collectible',
    },
  ] as FigurineStyle[],

  /**
   * General prompts for different figurine generation scenarios
   * These can be used independently or combined with style-specific prompts
   */
  prompts: {
    /**
     * Default prompt for figurine generation
     * Creates a 1/7 scale commercial figurine with realistic styling
     */
    default:
      'create a 1/7 scale commercialized figure of the character in the illustration, in a realistic style and environment. Place the figure on a computer desk, using a circular transparent acrylic base without any text. On the computer screen, display the ZBrush modeling process of the figure. Next to the computer screen, place a BANDAI-style toy packaging box printed with the original artwork. The image quality must be high definition.',

    /**
     * Alternative prompt for more detailed figurine generation
     */
    detailed:
      "generate a highly detailed 1/7 scale collectible figurine of the character from the provided image. The figurine should have professional-grade sculpting with realistic textures, accurate proportions, and vibrant colors. Include a premium display base with the character's name, place it in a collector's display case with proper lighting, and show the original character artwork alongside for reference. Ensure museum-quality presentation and photorealistic rendering.",

    /**
     * Prompt for action pose figurines
     */
    actionPose:
      "create a dynamic 1/7 scale action figurine of the character in an iconic pose from the source material. The figurine should capture movement and energy, with flowing elements like hair or clothing. Include a themed diorama base that represents the character's environment. Show the figurine from multiple angles with professional photography lighting. The overall presentation should feel like a premium collectible from companies like Good Smile or Alter.",
  },

  /**
   * Helper function to get style by id
   */
  getStyleById: (id: string) => {
    return FIGURINE_CONFIG.styles.find((style) => style.id === id);
  },

  /**
   * Helper function to get styles by category
   */
  getStylesByCategory: (category: FigurineStyle['category']) => {
    return FIGURINE_CONFIG.styles.filter(
      (style) => style.category === category
    );
  },
};

// Export individual parts for convenience
export const FIGURINE_STYLES = FIGURINE_CONFIG.styles;
export const FIGURINE_PROMPTS = FIGURINE_CONFIG.prompts;
