import type {
  ImageGenerationRequest,
  ImageGenerationResponse,
} from '@/lib/ai/types';
import { useState } from 'react';

interface UseImageGenerationState {
  loading: boolean;
  error: string | null;
  result: ImageGenerationResponse | null;
}

export function useImageGeneration() {
  const [state, setState] = useState<UseImageGenerationState>({
    loading: false,
    error: null,
    result: null,
  });

  const generateImage = async (request: ImageGenerationRequest) => {
    setState({
      loading: true,
      error: null,
      result: null,
    });

    try {
      let body: FormData | string;
      let headers: HeadersInit;

      // Use FormData if there's an image, otherwise use JSON
      if (request.image) {
        const formData = new FormData();
        formData.append('prompt', request.prompt);
        if (request.width) formData.append('width', request.width.toString());
        if (request.height)
          formData.append('height', request.height.toString());
        formData.append('image', request.image);

        body = formData;
        headers = {}; // Let browser set Content-Type with boundary
      } else {
        body = JSON.stringify(request);
        headers = {
          'Content-Type': 'application/json',
        };
      }

      const response = await fetch('/api/ai/generate-images', {
        method: 'POST',
        headers,
        body,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.error || `Generation failed: ${response.status}`
        );
      }

      const result: ImageGenerationResponse = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Generation failed');
      }

      setState({
        loading: false,
        error: null,
        result,
      });

      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      setState({
        loading: false,
        error: errorMessage,
        result: null,
      });
      throw error;
    }
  };

  const reset = () => {
    setState({
      loading: false,
      error: null,
      result: null,
    });
  };

  return {
    ...state,
    generateImage,
    reset,
  };
}
