'use client';

import { Button } from '@/components/ui/button';
import { Upload } from 'lucide-react';
import { useEffect, useState } from 'react';

interface ImageUploadProps {
  onImageChange: (file: File | null) => void;
  disabled?: boolean;
  accept?: string;
  title?: string;
  description?: string;
  className?: string;
}

export function ImageUpload({
  onImageChange,
  disabled = false,
  accept = 'image/*',
  title = 'Select Image',
  description = 'Supports JPG, PNG, WebP formats',
  className,
}: ImageUploadProps) {
  const [uploadedImage, setUploadedImage] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);

  // Clean up object URLs to prevent memory leaks
  useEffect(() => {
    if (uploadedImage) {
      const url = URL.createObjectURL(uploadedImage);
      setPreviewUrl(url);
      return () => URL.revokeObjectURL(url);
    }
    setPreviewUrl(null);
  }, [uploadedImage]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;

    // Validate file if exists
    if (file) {
      // Check file size (max 10MB)
      const maxSize = 10 * 1024 * 1024; // 10MB in bytes
      if (file.size > maxSize) {
        alert('File size must be less than 10MB');
        e.target.value = ''; // Reset input
        return;
      }

      // Check file type
      const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
      if (!validTypes.includes(file.type)) {
        alert('Please upload a valid image file (JPG, PNG, or WebP)');
        e.target.value = ''; // Reset input
        return;
      }
    }

    setUploadedImage(file);
    onImageChange(file);
  };

  return (
    <div
      className={`border border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors bg-gray-50 ${className}`}
    >
      <input
        type="file"
        accept={accept}
        onChange={handleFileChange}
        disabled={disabled}
        className="hidden"
        id="image-upload"
      />
      <label htmlFor="image-upload" className="cursor-pointer block">
        {uploadedImage && previewUrl ? (
          <div className="space-y-4">
            <img
              src={previewUrl}
              alt="Uploaded preview"
              className="max-h-48 mx-auto rounded-lg shadow-md"
            />
            <div className="space-y-2">
              <p className="text-sm font-medium text-gray-700">
                Selected: {uploadedImage.name}
              </p>
              <Button
                type="button"
                variant="outline"
                size="sm"
                className="border border-gray-300 text-gray-700 hover:bg-gray-100 cursor-pointer"
                disabled={disabled}
                onClick={(e) => {
                  e.preventDefault();
                  document.getElementById('image-upload')?.click();
                }}
              >
                Change Image
              </Button>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="text-gray-400 text-6xl">📷</div>
            <div className="space-y-2">
              <p className="text-lg font-medium text-gray-700">{title}</p>
              <p className="text-sm text-gray-500">{description}</p>
            </div>
            <Button
              type="button"
              variant="outline"
              className="border border-gray-300 text-gray-700 hover:bg-gray-100 cursor-pointer"
              disabled={disabled}
              onClick={(e) => {
                e.preventDefault();
                document.getElementById('image-upload')?.click();
              }}
            >
              <Upload className="size-4 mr-2" />
              Select Image
            </Button>
          </div>
        )}
      </label>
    </div>
  );
}
