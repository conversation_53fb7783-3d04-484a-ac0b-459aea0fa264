'use client';

import { Textarea } from '@/components/ui/textarea';

interface PromptInputProps {
  value: string;
  onChange: (value: string) => void;
  disabled?: boolean;
  placeholder?: string;
  label?: string;
  className?: string;
  maxLength?: number;
}

export function PromptInput({
  value,
  onChange,
  disabled,
  placeholder = 'Describe the image you want to generate...',
  label = 'Prompt',
  className,
  maxLength = 500,
}: PromptInputProps) {
  return (
    <div className="space-y-2">
      <label htmlFor="prompt-input" className="text-sm font-medium">
        {label}
      </label>
      <Textarea
        id="prompt-input"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        disabled={disabled}
        placeholder={placeholder}
        className={`min-h-20 resize-none ${className}`}
        maxLength={maxLength}
      />
      <p className="text-xs text-gray-500">
        {value.length}/{maxLength} characters
      </p>
    </div>
  );
}
