'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Sparkles } from 'lucide-react';
import { useTranslations } from 'next-intl';

interface GenerateButtonProps {
  onClick: () => void;
  loading: boolean;
  disabled?: boolean;
  credits: number;
  className?: string;
}

export function GenerateButton({
  onClick,
  loading,
  disabled = false,
  credits,
  className = '',
}: GenerateButtonProps) {
  const tCommon = useTranslations('AIApps.common');

  return (
    <Button
      onClick={onClick}
      disabled={loading || disabled}
      className={`w-full h-12 text-lg bg-gradient-to-r from-orange-500 to-amber-500 hover:from-orange-600 hover:to-amber-600 text-white font-semibold cursor-pointer disabled:cursor-not-allowed ${className}`}
    >
      {loading ? (
        <>
          <div className="animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent mr-2" />
          {tCommon('generate.generating')}
        </>
      ) : (
        <>
          <Sparkles className="size-5 mr-2" />
          {tCommon('generate.button')} ·{' '}
          {tCommon('generate.credits', { count: credits })}
        </>
      )}
    </Button>
  );
}
