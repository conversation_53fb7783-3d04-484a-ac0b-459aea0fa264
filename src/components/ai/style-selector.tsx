'use client';

import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { useTranslations } from 'next-intl';
import { useState } from 'react';

export interface Style {
  id: string;
  name: string;
  preview: string;
  prompt: string;
}

interface StyleSelectorProps {
  styles: Style[];
  onStyleSelect: (style: Style) => void;
  selectedStyleId?: string;
  disabled?: boolean;
  className?: string;
}

export function StyleSelector({
  styles,
  onStyleSelect,
  selectedStyleId,
  disabled = false,
  className,
}: StyleSelectorProps) {
  const [selectedStyle, setSelectedStyle] = useState<string | undefined>(
    selectedStyleId
  );
  const t = useTranslations('AIApps.common.styleSelector');

  const handleStyleSelect = (style: Style) => {
    if (disabled) return;

    setSelectedStyle(style.id);
    onStyleSelect(style);
  };

  return (
    <div className={className}>
      {/* Scrollable Grid Container - Show exactly 2 rows */}
      <div
        style={{ maxHeight: '280px' }}
        className="overflow-y-auto overscroll-contain"
      >
        <div className="flex flex-wrap gap-4">
          {styles.map((style) => (
            <div
              key={style.id}
              className={cn(
                'border-2 border-border rounded-lg overflow-hidden bg-muted/20 aspect-square cursor-pointer transition-all duration-300 hover:border-orange-500/50 hover:shadow-lg',
                selectedStyle === style.id &&
                  'border-orange-500 ring-2 ring-orange-500/30 shadow-xl',
                disabled && 'opacity-50 cursor-not-allowed hover:border-border'
              )}
              style={{ width: 'calc(33.333% - 0.75rem)' }}
              onClick={() => handleStyleSelect(style)}
            >
              <div className="relative w-full h-full">
                {style.preview ? (
                  // eslint-disable-next-line @next/next/no-img-element
                  <img
                    src={style.preview}
                    alt={`${style.name} preview`}
                    className="w-full h-full object-cover"
                    loading="lazy"
                    onError={(e) => {
                      // Fallback to placeholder if image fails to load
                      const target = e.target as HTMLImageElement;
                      target.style.display = 'none';
                      const fallback = target.nextElementSibling as HTMLElement;
                      if (fallback) fallback.style.display = 'flex';
                    }}
                  />
                ) : null}
                {/* Fallback placeholder */}
                <div
                  className="absolute inset-0 bg-gradient-to-br from-muted-foreground/20 to-muted-foreground/40 flex items-center justify-center text-muted-foreground"
                  style={{ display: style.preview ? 'none' : 'flex' }}
                >
                  <div className="text-center px-2">
                    <div className="text-sm font-medium">{style.name}</div>
                    <div className="text-xs opacity-70 mt-1">Preview</div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
