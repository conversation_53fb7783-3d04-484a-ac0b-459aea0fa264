'use client';

import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { uploadFileFromBrowser } from '@/storage/client';
import { X } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useCallback, useEffect, useState } from 'react';

interface UploadedImage {
  file: File;
  url: string;
  uploadUrl?: string;
}

interface ImageUploadGridProps {
  onImagesChange: (files: File[]) => void;
  disabled?: boolean;
  maxImages?: number;
  className?: string;
}

export function ImageUploadGrid({
  onImagesChange,
  disabled = false,
  maxImages = 9,
  className,
}: ImageUploadGridProps) {
  const [uploadedImages, setUploadedImages] = useState<UploadedImage[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const { toast } = useToast();
  const t = useTranslations('AIApps.common');

  // Clean up object URLs when component unmounts
  useEffect(() => {
    return () => {
      uploadedImages.forEach((image) => {
        URL.revokeObjectURL(image.url);
      });
    };
  }, []);

  // Update parent component when images change
  useEffect(() => {
    onImagesChange(uploadedImages.map((img) => img.file));
  }, [uploadedImages, onImagesChange]);

  const validateFile = useCallback(
    (file: File): boolean => {
      // Check file size (max 10MB)
      const maxSize = 10 * 1024 * 1024;
      if (file.size > maxSize) {
        toast({
          title: 'File too large',
          description: 'File size must be less than 10MB',
          variant: 'destructive',
        });
        return false;
      }

      // Check file type
      const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
      if (!validTypes.includes(file.type)) {
        toast({
          title: 'Invalid file type',
          description: 'Please upload a valid image file (JPG, PNG, or WebP)',
          variant: 'destructive',
        });
        return false;
      }

      return true;
    },
    [toast]
  );

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);

    if (files.length === 0) return;

    // Check if adding these files would exceed max limit
    if (uploadedImages.length + files.length > maxImages) {
      toast({
        title: t('errors.maxImagesReached.title'),
        description: t('errors.maxImagesReached.description'),
        variant: 'destructive',
      });
      e.target.value = ''; // Reset input
      return;
    }

    // Validate all files first
    const validFiles = files.filter(validateFile);
    if (validFiles.length === 0) {
      e.target.value = ''; // Reset input
      return;
    }

    setIsUploading(true);

    try {
      const newImages: UploadedImage[] = [];

      for (const file of validFiles) {
        // Create preview URL
        const previewUrl = URL.createObjectURL(file);

        try {
          // Upload to storage
          const { url: uploadUrl } = await uploadFileFromBrowser(
            file,
            'ai-images'
          );

          newImages.push({
            file,
            url: previewUrl,
            uploadUrl,
          });
        } catch (error) {
          console.error('Upload failed for file:', file.name, error);
          URL.revokeObjectURL(previewUrl);
          toast({
            title: 'Upload failed',
            description: `Failed to upload ${file.name}`,
            variant: 'destructive',
          });
        }
      }

      if (newImages.length > 0) {
        setUploadedImages((prev) => [...prev, ...newImages]);
      }
    } finally {
      setIsUploading(false);
      e.target.value = ''; // Reset input
    }
  };

  const removeImage = useCallback((index: number) => {
    setUploadedImages((prev) => {
      const newImages = [...prev];
      const removedImage = newImages[index];

      // Clean up object URL
      URL.revokeObjectURL(removedImage.url);

      newImages.splice(index, 1);
      return newImages;
    });
  }, []);

  return (
    <div className={className}>
      <div className="flex flex-wrap gap-4">
        {/* Display uploaded images */}
        {uploadedImages.map((image, index) => (
          <div
            key={index}
            className="border-2 border-border rounded-lg overflow-hidden bg-muted/20 aspect-square"
            style={{ width: 'calc(33.333% - 0.75rem)' }}
          >
            <div className="relative w-full h-full">
              <img
                src={image.url}
                alt={`Upload #${index + 1}`}
                className="w-full h-full object-cover"
              />
              <Button
                size="sm"
                variant="destructive"
                className="absolute top-1 right-1 h-5 w-5 p-0 rounded-full"
                onClick={() => removeImage(index)}
                disabled={disabled || isUploading}
              >
                <X className="h-2 w-2" />
              </Button>
            </div>
          </div>
        ))}

        {/* Add button if under limit */}
        {uploadedImages.length < maxImages && (
          <div
            className="border-2 border-dashed border-border rounded-lg overflow-hidden bg-muted/20 hover:border-muted-foreground/50 transition-colors aspect-square"
            style={{ width: 'calc(33.333% - 0.75rem)' }}
          >
            <label className="flex flex-col items-center justify-center w-full h-full cursor-pointer hover:bg-muted/40 transition-colors">
              <input
                type="file"
                accept="image/*"
                multiple
                onChange={handleFileChange}
                disabled={disabled || isUploading}
                className="hidden"
              />
              <div className="text-center">
                <div className="text-xl text-muted-foreground/60 mb-1">
                  {isUploading ? '⏳' : '+'}
                </div>
                <p className="text-xs text-foreground">
                  {isUploading
                    ? t('uploadImage.uploading')
                    : t('uploadImage.addImage')}
                </p>
              </div>
            </label>
          </div>
        )}
      </div>


    </div>
  );
}
