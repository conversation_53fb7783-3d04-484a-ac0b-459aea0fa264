'use client';

import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useImageGeneration } from '@/hooks/use-image-generation';
import { useToast } from '@/hooks/use-toast';
import type { ImageGenerationRequest } from '@/lib/ai/types';
import { useState } from 'react';
import { ImageResult } from './image-result';
import { PromptInput } from './prompt-input';

interface ImageGeneratorProps {
  title?: string;
  defaultPrompt?: string;
  onImageGenerated?: (imageUrl: string) => void;
  uploadedImage?: File | null;
}

export function ImageGenerator({
  title = 'AI Image Generator',
  defaultPrompt = '',
  onImageGenerated,
  uploadedImage,
}: ImageGeneratorProps) {
  const [prompt, setPrompt] = useState(defaultPrompt);
  const { toast } = useToast();
  const { loading, error, result, generateImage } = useImageGeneration();

  const handleGenerate = async () => {
    if (!prompt.trim()) {
      toast({
        title: 'Prompt cannot be empty',
        description:
          'Please enter a description for the image you want to generate',
        variant: 'destructive',
      });
      return;
    }

    // For figurine generation, require an uploaded image
    if (title.toLowerCase().includes('figurine') && !uploadedImage) {
      toast({
        title: 'Please upload an image',
        description: 'Figurine generation requires uploading an original image',
        variant: 'destructive',
      });
      return;
    }

    try {
      const request: ImageGenerationRequest = {
        prompt,
        image: uploadedImage || undefined,
      };

      const result = await generateImage(request);

      if (result?.imageUrl && onImageGenerated) {
        onImageGenerated(result.imageUrl);
      }
    } catch (error) {
      // Error is already handled by the hook
      console.error('Generation failed:', error);
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="text-center">{title}</CardTitle>
      </CardHeader>

      <CardContent className="space-y-6">
        <PromptInput value={prompt} onChange={setPrompt} disabled={loading} />

        <Button
          onClick={handleGenerate}
          disabled={loading || !prompt.trim()}
          className="w-full h-12 text-lg"
          size="lg"
        >
          {loading ? 'Generating...' : '✨ Generate Image'}
        </Button>

        <ImageResult result={result} loading={loading} error={error} />
      </CardContent>
    </Card>
  );
}
